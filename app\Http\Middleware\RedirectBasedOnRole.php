<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RedirectBasedOnRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        // If user is not authenticated, continue
        if (!$user) {
            return $next($request);
        }

        // Get current route
        $currentRoute = $request->route()->getName();

        // If user is trying to access login pages while already authenticated
        if (in_array($currentRoute, ['filament.admin.auth.login', 'filament.karyawan.auth.login'])) {
            // Redirect based on role
            // switch ($user->role) {
            //     case 'admin':
            //     case 'supervisor':
            //         return redirect()->route('filament.admin.pages.dashboard');
            //     case 'karyawan':
            //         return redirect()->route('filament.karyawan.pages.dashboard');
            //     default:
            //         // If no valid role, logout and continue to login
            //         Auth::logout();
            //         $request->session()->invalidate();
            //         $request->session()->regenerateToken();
            //         break;
            // }
            if ($user->hasAnyRole(['super_admin', 'direktur', 'manager_hrd', 'manager_accounting'])) {
                return redirect()->route('filament.admin.pages.dashboard');
            } elseif ($user->hasAnyRole(['karyawan'])) {
                return redirect()->route('filament.karyawan.pages.dashboard');
            } else {
                Auth::logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();
            }
        }

        return $next($request);
    }
}
