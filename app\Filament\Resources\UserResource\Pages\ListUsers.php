<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;


class ListUsers extends ListRecords
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    // protected function getHeaderWidgets(): array
    // {
    //     return [
    //         \App\Filament\Widgets\UserStatsOverview::class,
    //     ];
    // }

     public function getHeaderWidgets(): array
    {
        return [
            \App\Filament\Widgets\UserStatsOverview::class,
            // \App\Filament\Widgets\AttendanceAlertsWidget::class,
            // \App\Filament\Widgets\AttendanceAnalyticsWidget::class,
        ];
    }
}
